{assign name="yunyoo_var" value="1.0.0" /}

<link rel="shortcut icon" href="/themes/clientarea/yunyoo/assets/images/favicon.ico">
<link href="/themes/clientarea/yunyoo/assets/css/bootstrap.min.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" type="text/css" />
<link href="/themes/clientarea/yunyoo/assets/css/icons.min.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" type="text/css" />
<link href="/themes/clientarea/yunyoo/assets/css/app.min.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" type="text/css" />

<link href="/themes/clientarea/yunyoo/assets/css/yunyoo.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" type="text/css" />

<link href="/themes/clientarea/yunyoo/assets/css/DINCondensed-Bold.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" type="text/css" />
<link href="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.css" type="text/css" rel="stylesheet" />
{if($load_css=load_css('custom.css'))}
    <link href="{$load_css}?v={$Ver}" rel="stylesheet" type="text/css" />
{/if}
<!-- 自定义全局样式 -->
<link href="/themes/clientarea/yunyoo/assets_custom/css/global.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" >
<link href="/themes/clientarea/yunyoo/assets_custom/css/responsive.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet">
<!-- 字体图标 -->

<link href="/themes/clientarea/yunyoo/assets_custom/fonts/iconfont.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet"> 

<!-- JAVASCRIPT -->
<script src="/themes/clientarea/yunyoo/assets/libs/jquery/jquery.min.js?v={$yunyoo_var}.{$Ver}"></script>
<script src="/themes/clientarea/yunyoo/assets/libs/bootstrap/js/bootstrap.bundle.min.js?v={$yunyoo_var}.{$Ver}"></script>
<script src="/themes/clientarea/yunyoo/assets/libs/metismenu/metisMenu.min.js?v={$yunyoo_var}.{$Ver}"></script>
<script src="/themes/clientarea/yunyoo/assets/libs/simplebar/simplebar.min.js?v={$yunyoo_var}.{$Ver}"></script>
<script src="/themes/clientarea/yunyoo/assets/libs/node-waves/waves.min.js?v={$yunyoo_var}.{$Ver}"></script>

<!-- 自定义js -->
<script src="/themes/clientarea/yunyoo/assets_custom/js/throttle.js?v={$yunyoo_var}.{$Ver}"></script>

<link type="text/css" href="/themes/clientarea/yunyoo/assets/libs/toastr/build/toastr.min.css?v={$yunyoo_var}.{$Ver}" rel="stylesheet" />
<script src="/themes/clientarea/yunyoo/assets/libs/toastr/build/toastr.min.js?v={$yunyoo_var}.{$Ver}"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>


