/*!
 * Bootstrap-select v1.14.0-beta (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2020 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,n){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return n(e)}):"object"==typeof module&&module.exports?module.exports=n(require("jquery")):n(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"Vyberte zo zoznamu",noneResultsText:"Pre v\xfdraz {0} neboli n\xe1jden\xe9 \u017eiadne v\xfdsledky",countSelectedText:"Vybran\xe9 {0} z {1}",maxOptionsText:["Limit prekro\u010den\xfd ({n} {var} max)","Limit skupiny prekro\u010den\xfd ({n} {var} max)",["polo\u017eiek","polo\u017eka"]],selectAllText:"Vybra\u0165 v\u0161etky",deselectAllText:"Zru\u0161i\u0165 v\xfdber",multipleSeparator:", "}});