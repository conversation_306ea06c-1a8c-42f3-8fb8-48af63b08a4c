/*!
 * Bootstrap-select v1.14.0-beta (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2020 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"Ni\u010d izbranega",noneResultsText:"Ni zadetkov za {0}",countSelectedText:"{0} od {1} izbranih",maxOptionsText:function(e,t){return["Omejitev dose\u017eena (max. izbranih: {n})","Omejitev skupine dose\u017eena (max. izbranih: {n})"]},selectAllText:"Izberi vse",deselectAllText:"Po\u010disti izbor",multipleSeparator:", "}});