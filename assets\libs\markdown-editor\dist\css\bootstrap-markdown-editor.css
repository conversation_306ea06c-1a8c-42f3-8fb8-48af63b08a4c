/*!
 * Bootstrap Markdown Editor v1.2.1 (https://github.com/inacho/bootstrap-markdown-editor)
 * Copyright 2016 <PERSON> <<EMAIL>>
 * Licensed under MIT (https://github.com/inacho/bootstrap-markdown-editor/blob/master/LICENSE)
 */

.md-container{position:relative}.md-loading{display:none;filter:alpha(opacity=80);opacity:.8;position:absolute;top:0;bottom:0;left:0;right:0;background:#fff;z-index:9999}.md-icon-container{display:table;width:100%;height:100%}.md-icon{height:100%;display:table-cell;vertical-align:middle;text-align:center}.md-icon:before{content:"\e031";font-family:"Glyphicons Halflings";font-size:80px;line-height:1;-animation:spin 1s infinite linear;-ms-animation:spin 1s infinite linear;-webkit-animation:spinw 1s infinite linear;-moz-animation:spinm 1s infinite linear}body.md-body-fullscreen,html.md-body-fullscreen{overflow:hidden;height:100%;width:100%;margin:0;padding:0}.md-toolbar{padding:10px;background:#f7f7f7;border:1px solid #d8d8d8;border-bottom:none;border-top-left-radius:4px;border-top-right-radius:4px}.md-toolbar button{font-weight:bold}.md-toolbar button:focus,.md-toolbar button:active{outline:none !important}.md-btn-file{position:relative;overflow:hidden}.md-btn-file input[type=file]{position:absolute;top:0;right:0;min-width:100%;min-height:100%;font-size:100px;text-align:right;filter:alpha(opacity=0);opacity:0;outline:none;background:white;cursor:inherit;display:block}.md-editor,.md-preview{border:1px solid #d8d8d8;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.md-preview{overflow-y:scroll;padding:10px}.md-fullscreen{position:absolute;margin:0 !important;top:0;bottom:0;left:0;right:0;width:100% !important;background:#fff;z-index:9999}.md-fullscreen .md-toolbar{border:none}.md-fullscreen .md-editor,.md-fullscreen .md-preview{border:none;width:100% !important}@keyframes spin{from{transform:scale(1) rotate(0deg)}to{transform:scale(1) rotate(360deg)}}@-webkit-keyframes spinw{from{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(360deg)}}@-moz-keyframes spinm{from{-moz-transform:rotate(0deg)}to{-moz-transform:rotate(360deg)}}