{"version": 3, "sources": ["../../../js/i18n/defaults-zh_CN.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,UAAU,CAAC;AACjC,IAAI,eAAe,CAAC,CAAC,UAAU,CAAC;AAChC,IAAI,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,IAAI,cAAc,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;AAC9D,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAC7B,IAAI,aAAa,CAAC,CAAC,KAAK,CAAC;AACzB,IAAI,eAAe,CAAC,CAAC,MAAM,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-zh_CN.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: '没有选中任何项',\r\n    noneResultsText: '没有找到匹配项',\r\n    countSelectedText: '选中{1}中的{0}项',\r\n    maxOptionsText: ['超出限制 (最多选择{n}项)', '组选择超出限制(最多选择{n}组)'],\r\n    multipleSeparator: ', ',\r\n    selectAllText: '全选',\r\n    deselectAllText: '取消全选'\r\n  };\r\n})(jQuery);\r\n"]}