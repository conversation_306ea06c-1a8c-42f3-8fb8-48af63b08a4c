# Idcsmart YUNYOO Client Area Template

Language: English | [简体中文](README.md)

![1](https://github.com/user-attachments/assets/b5b43692-197c-44b2-8814-164144deb5d6)



## Introduction

**YUNYOO Themes** is a theme template based on the IdcsmartFinance system. It features an intuitive and clean interface design, ensuring a smooth user experience while maintaining simplicity.

YUNYOO Themes is open-source and free under the Apache License 2.0. You are free to use and modify it as you wish.

YUNYOO Themes will be continuously maintained, updated, and improved, with more features and enhancements being added regularly.

***This template is currently in its initial version and is still being improved. :)***



## Demo

- [Login](https://yunyoo.cc/login)  
- [Register](https://yunyoo.cc/register)  
- [Client Area](https://yunyoo.cc/clientarea)  



## Screenshots

| Client Area | [Shopping Cart](https://github.com/yunyoo-opensource/idcsmart-yunyoo-cart) |  
| --- | --- |  
| ![2](https://github.com/user-attachments/assets/bbebd6f8-05a3-4a38-b949-b902c5a300f4) | ![3](https://github.com/user-attachments/assets/47366b58-00c2-4040-93c4-6223cdacebed) |  

| Login Page | Registration Page |  
| --- | --- |  
| ![4](https://github.com/user-attachments/assets/6eb3c84b-2351-433a-822f-f1903973f014) | ![5](https://github.com/user-attachments/assets/827f77d3-c6d6-45eb-88b2-bdc78d8da6c8) |  



## Features

- **Simple & Easy to Use** – Intuitive interface design that simplifies the user experience.  
- **Elegant & Modern** – Smooth interactions with a modern visual style.  
- **Open-Source & Free** – Follows an open-source license, allowing free use and modification.  



## Quick Start

1. Copy all files to the `/public/themes/clientarea` directory in your IdcsmartFinance installation directory
2. Change the template to `yunyoo` through your admin panel



## More

- Matching shopping cart template: [idcsmart-yunyoo-cart](https://github.com/yunyoo-opensource/idcsmart-yunyoo-cart)  



## Contact

- Email: `<EMAIL>`  



## License

This project is licensed under the Apache License 2.0 – See the [LICENSE](LICENSE) file for details.  

- If you wish to support my work, please retain the `YUNYOO LTD` copyright notice.  
