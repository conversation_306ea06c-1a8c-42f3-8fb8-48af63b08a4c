<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/10/nuspec.xsd">
    <metadata>
        <id>toastr</id>
        <version>2.0.0-rc1</version>
        <title>toastr</title>
        <authors><PERSON>,<PERSON></authors>
        <owners><PERSON>,<PERSON></owners>
        <licenseUrl>http://www.opensource.org/licenses/mit-license.php</licenseUrl>
        <projectUrl>http://toastrjs.com/</projectUrl>
        <iconUrl>http://johnpapa.net/wp-content/images/toastr-icon.png</iconUrl>
        <requireLicenseAcceptance>false</requireLicenseAcceptance>
        <description>3 Easy Steps:

(1) Link to toastr.css  
(2) Link to toastr.js 
(3) Use toastr to display a toast for info, success, warning or error

// Display an info toast with no title
toastr.info('Are you the 6 fingered man?')

*** For other API calls, see the demo</description>
        <summary>Toastr is a JavaScript library for non-blocking notifications. jQuery is required. The goal is to create a simple core library that can be customized and extended.</summary>
        <releaseNotes>The following animations options have been deprecated and should be replaced:

Replace options.fadeIn with options.showDuration
Replace options.onFadeIn with options.onShown
Replace options.fadeOut with options.hideDuration
Replace options.onFadeOut with options.onHidden

Optional 'close' button, bug fixes, and improved responsive design.

For other changes, see http://toastrjs.com</releaseNotes>
        <copyright>Copyright © 2012 Hans Fjällemark &amp; John Papa.</copyright>
        <language>en-US</language>
        <tags>toastr, toast, notification, dialog, jquery</tags>
        <dependencies>
            <dependency id="jQuery" version="1.6.3" />
        </dependencies>
    </metadata>
    <files>
        <file src="content\content\toastr.css" target="content\content\toastr.css" />
        <file src="content\content\toastr.less" target="content\content\toastr.less" />
        <file src="content\content\toastr.min.css" target="content\content\toastr.min.css" />
        <file src="content\scripts\toastr.js" target="content\scripts\toastr.js" />
        <file src="content\scripts\toastr.min.js" target="content\scripts\toastr.min.js" />
        <file src="content\scripts\toastr.min.js.map" target="content\scripts\toastr.min.js.map" />
    </files>
</package>