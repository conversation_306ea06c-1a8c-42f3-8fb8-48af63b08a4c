/*!
 * Bootstrap-select v1.14.0-beta (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2020 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"Valikut pole tehtud",noneResultsText:"Otsingule {0} ei ole vasteid",countSelectedText:function(e,t){return 1==e?"{0} item selected":"{0} items selected"},maxOptionsText:function(e,t){return["Limiit on {n} max","Globaalne limiit on {n} max"]},selectAllText:"Vali k\xf5ik",deselectAllText:"T\xfchista k\xf5ik",multipleSeparator:", "}});