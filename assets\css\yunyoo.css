@media (min-width: 768px){
.yunyoo-login {
    padding: 3rem 18% !important;
}
}
.yunyoo-login {
    padding: 0 12% ;
}
@media (min-width: 1200px){
.yunyoo-login {
    padding: 0 12% !important;
}
}
.icon-sm {
    width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    text-align: center;
    font-size: 75%;
}
#page-topbar {
    background-color: #ffffffbd !important;
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
}
#avatar {
    opacity: 0;
}
.link {
    cursor: pointer;
}
.login-info-icon {
    color: var(--blue) !important;
}
.yunyoo-num {
    font-family: DINCondensed-Bold;
}
.yunyoo-num small {
    font-size: 12px;
    margin-left: 5px;
}
.fs-6 {
    font-size: 1rem !important;
}
.me-2 {
    margin-right: 0.5rem !important;
}
.main-content * {
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.shadow {
    -webkit-box-shadow: 0px 0px 40px rgb(29 58 83 / 10%) !important;
    box-shadow: 0px 0px 40px rgb(29 58 83 / 10%) !important;
}
select {
    word-wrap: normal;
    color: var(--bs-global);
    padding: 0.15em;
    line-height: var(--bs-btn-line-height);
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
    border-radius: 100em;
}
.server_header_box {
    background-image: linear-gradient(87deg, var(--bs-btn-bg) 0%, var(--bs-btn-active-border-color) 100%) !important;
}
.car-num {
    font-family: DINCondensed-Bold;
    font-size: 22px;
}
.row.align-items-center.server_header_box .custom-button, .row.align-items-center.server_header_box .left_wrap_btn {
    background-color: var(--blue) !important;
    box-shadow: 0px 6px 14px 2px rgb(122 197 205 / 50%) !important;
}
.powerimg {
    width: 48px !important;
    height: 48px !important;
    box-shadow: 0px 6px 14px 2px rgb(122 197 205 / 50%) !important;
    border-radius: 50% !important;
}
.left_wrap_btn {
    box-shadow: inherit  !important;
}
.dc {
    color: var(--primary) !important;
}
.configuration-btn-down {
    color: var(--primary) !important;
}
.configuration-btn-down::after {
    border: 1px solid var(--primary) !important;
}
.left_wrap_btn {
    background-color: var(--primary) !important;
}
.c-red {
    color: red;
}
.tips {
    border-left: 5px solid var(--primary);
    background-color: var(--bs-global-1);
    border-radius: var(--bs-btn-border-radius);
    padding: 0.5em 12px !important;
}
.modal.fade.show {
    -webkit-backdrop-filter: brightness(0.5)blur(6px);
    backdrop-filter: brightness(0.5)blur(6px);
}
.modal-content {
    -webkit-box-shadow: 0px 0px 40px rgb(29 58 83 / 10%) !important;
    box-shadow: 0px 0px 40px rgb(29 58 83 / 10%) !important;
}
.wspace::before {
  content: "\00a0"; /* 使用不断行的空格 */
}
.wspace::after {
  content: "\00a0"; /* 使用不断行的空格 */
}
.yunyoo-c-primary {
    color: var(--primary);
}
.yunyoo-br-primary {
  border-radius: var(--bs-btn-border-radius);
}
.badge-soft-yunyoo {
    color: var(--bs-global);
    background-color: rgba(122, 197, 205, 0.18);
}
.bl-hover:hover, .bl-hov:hover {
    border-width: 8px;
}
.bl-active:active, .bl-hov:active {
    border-width: 3px;
}
.bl-global {
    border-left: 5px solid var(--bs-global);
    border-radius: var(--bs-btn-border-radius);
}
.bl-blue {
  border-left: 5px solid #228cfc;
  border-radius: var(--bs-btn-border-radius);
}
.bl-indigo {
  border-left: 5px solid var(--indigo);
  border-radius: var(--bs-btn-border-radius);
}

.bl-purple {
  border-left: 5px solid var(--purple);
  border-radius: var(--bs-btn-border-radius);
}

.bl-pink {
  border-left: 5px solid var(--pink);
  border-radius: var(--bs-btn-border-radius);
}

.bl-red {
  border-left: 5px solid var(--red);
  border-radius: var(--bs-btn-border-radius);
}

.bl-orange {
  border-left: 5px solid var(--orange);
  border-radius: var(--bs-btn-border-radius);
}

.bl-yellow {
  border-left: 5px solid var(--yellow);
  border-radius: var(--bs-btn-border-radius);
}

.bl-green {
  border-left: 5px solid var(--green);
  border-radius: var(--bs-btn-border-radius);
}

.bl-teal {
  border-left: 5px solid var(--teal);
  border-radius: var(--bs-btn-border-radius);
}

.bl-cyan {
  border-left: 5px solid var(--cyan);
  border-radius: var(--bs-btn-border-radius);
}

.bl-white {
  border-left: 5px solid var(--white);
  border-radius: var(--bs-btn-border-radius);
}

.bl-gray {
  border-left: 5px solid var(--gray);
  border-radius: var(--bs-btn-border-radius);
}

.bl-gray-dark {
  border-left: 5px solid var(--gray-dark);
  border-radius: var(--bs-btn-border-radius);
}

.bl-primary {
  border-left: 5px solid var(--primary);
  border-radius: var(--bs-btn-border-radius);
}

.bl-secondary {
  border-left: 5px solid var(--secondary);
  border-radius: var(--bs-btn-border-radius);
}

.bl-success {
  border-left: 5px solid var(--success);
  border-radius: var(--bs-btn-border-radius);
}

.bl-info {
  border-left: 5px solid var(--info);
  border-radius: var(--bs-btn-border-radius);
}

.bl-warning {
  border-left: 5px solid var(--warning);
  border-radius: var(--bs-btn-border-radius);
}

.bl-danger {
  border-left: 5px solid var(--danger);
  border-radius: var(--bs-btn-border-radius);
}

.bl-light {
  border-left: 5px solid var(--light);
  border-radius: var(--bs-btn-border-radius);
}

.bl-dark {
  border-left: 5px solid var(--dark);
  border-radius: var(--bs-btn-border-radius);
}

pre.newscontent {
    line-height: 2em;
}

:root {
    --bs-global: #7AC5CD;
    --bs-global-1: rgba(122, 197, 205, 0.1);
    --bs-global-2: rgba(122, 197, 205, 0.1);
    --bs-global-3: #96CDCD;
    --bs-global-o-1: rgba(122, 197, 205, 0.1);
    --bs-global-o-2: rgba(122, 197, 205, 0.2);
    --bs-global-o-3: rgba(122, 197, 205, 0.3);
    --bs-global-o-4: rgba(122, 197, 205, 0.4);
    --bs-global-o-5: rgba(122, 197, 205, 0.5);
    --bs-global-o-0: rgba(122, 197, 205, 0);
    --bs-btn-color: #fff;
    --bs-btn-bg: var(--bs-global);
    --bs-btn-border-color: var(--bs-global);
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #668B8B;
    --bs-btn-hover-border-color: #668B8B;
    --bs-btn-focus-shadow-rgb: 107, 95, 223;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #668B8B;
    --bs-btn-active-border-color: #668B8B;
    --bs-btn-active-shadow: none;
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: var(--bs-global);
    --bs-btn-disabled-border-color: var(--bs-global);
    --bs-btn-padding-x: 1rem;
    --bs-btn-padding-y: 0.5rem;
    --bs-btn-font-family: ;
    --bs-btn-font-size: 1rem;
    --bs-btn-font-weight: 700;
    --bs-btn-line-height: 1.5;
    --bs-btn-border-width: 1px;
    --bs-btn-border-radius: 0.5rem;
    --bs-btn-box-shadow: none;
    --bs-btn-disabled-opacity: 0.65;
    --bs-btn-focus-box-shadow: 0 0 0 0 rgba(var(--bs-btn-focus-shadow-rgb), .5);
}

/* 让 select 内容垂直居中 */
.dropdown.bootstrap-select.show-tick .btn {
  display: flex;
  align-items: center;
}

/* 让搜索框输入内容垂直居中 */
.search-box {
  position: relative;
}
.search-box .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #7AC5CD;
  pointer-events: none;
}

/* 统一描边圆角风格：蓝色描边、白底、黑字、圆角 */
.dropdown.bootstrap-select.show-tick .btn,
.btn.btn-sm.btn-primary.w-xs,
.search-box input[type="text"],
.search-box input[type="search"] {
  background: #fff;
  color: #222;
  border: 2px solid #7AC5CD;
  border-radius: 20px;
  box-sizing: border-box;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  padding: 0 16px;
  transition: border-color 0.2s;
}

/* 鼠标悬停/激活时描边加深 */
.dropdown.bootstrap-select.show-tick .btn:hover,
.btn.btn-sm.btn-primary.w-xs:hover,
.search-box input[type="text"]:focus,
.search-box input[type="search"]:focus {
  border-color: #668B8B;
  outline: none;
}

/* 按钮特殊：保持主色描边，背景白，文字黑 */
.btn.btn-sm.btn-primary.w-xs {
  background: #fff !important;
  color: #222 !important;
  border: 2px solid #7AC5CD !important;
}

/* 修复搜索框图标和文字重叠问题 */
.search-box input[type="text"],
.search-box input[type="search"] {
  padding-left: 40px !important;
}

/* 只还原 .btn-group 下非 .dropdown-toggle 的按钮，避免控制按钮变小 */
.btn-group .btn:not(.dropdown-toggle),
.btn-group .btn.btn-sm:not(.dropdown-toggle),
.btn-group .btn.btn-primary:not(.dropdown-toggle),
.btn-group .btn.btn-sm.btn-primary.w-xs:not(.dropdown-toggle) {
  background: initial !important;
  color: initial !important;
  border: initial !important;
  border-radius: 4px !important;
  height: auto !important;
  line-height: normal !important;
  font-size: inherit !important;
  padding: 0 12px !important;
  box-sizing: border-box;
  transition: none;
}

/* 保留批量操作按钮（.dropdown-toggle）的自定义大圆角描边风格 */

/* 批量操作按钮禁用时保持蓝绿色风格 */
.btn-group .dropdown-toggle:disabled,
.btn-group .dropdown-toggle.disabled {
  background: #e6f6f8 !important;   /* 浅蓝绿色背景 */
  color: #b2cfd1 !important;        /* 浅色文字 */
  border: 1px solid #b2cfd1 !important;
  opacity: 1 !important;            /* 避免变淡 */
  cursor: not-allowed !important;
}

/* 批量操作按钮可用状态保持主色描边和白底，防止选中后变黑 */
.btn-group .dropdown-toggle:not(:disabled):not(.disabled) {
  background: #fff !important;
  color: #7AC5CD !important;
  border: 1px solid #7AC5CD !important;
  border-radius: 6px !important;
  box-shadow: none !important;
}

/* 备注信息和登录信息区域背景色统一为灰色（RGB:245,245,245） */
.bg-light.card-body.bg-gray {
  background-color: #f5f5f5 !important;
}

/* 覆盖登录和注册页面的紫色文字，改为青色 */
.text-primary {
  color: #17a2b8 !important;
}

a.text-primary:focus,
a.text-primary:hover {
  color: #138496 !important;
}

/* 将页脚背景改为白色 */
.footer {
  background-color: #ffffff !important;
}

/* 让备注信息和上方卡片宽度、内边距、圆角完全一致 */
.col-12.mb-2 > .bg-light.card-body.bg-gray {
  margin: 0 !important;
  padding: 24px !important;
  width: 100% !important;
  box-sizing: border-box !important;
  border-radius: 8px !important;
}

/* 重装系统取消按钮样式 */
#dcimModuleReinstall .modal-footer .btn-secondary,
#moduleReinstall .modal-footer .btn-secondary {
  background-color: white !important;
  color: #495057 !important;
  border: 1px solid #ced4da !important;
}

#dcimModuleReinstall .modal-footer .btn-secondary:hover,
#moduleReinstall .modal-footer .btn-secondary:hover {
  background-color: #f8f9fa !important;
  border-color: #dae0e5 !important;
}