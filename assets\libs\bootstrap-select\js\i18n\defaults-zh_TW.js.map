{"version": 3, "sources": ["../../../js/i18n/defaults-zh_TW.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,WAAW,CAAC;AAClC,IAAI,eAAe,CAAC,CAAC,YAAY,CAAC;AAClC,IAAI,iBAAiB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AACrC,IAAI,cAAc,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;AAC3D,IAAI,aAAa,CAAC,CAAC,OAAO,CAAC;AAC3B,IAAI,eAAe,CAAC,CAAC,OAAO,CAAC;AAC7B,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-zh_TW.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: '沒有選取任何項目',\r\n    noneResultsText: '沒有找到符合的結果',\r\n    countSelectedText: '已經選取{0}個項目',\r\n    maxOptionsText: ['超過限制 (最多選擇{n}項)', '超過限制(最多選擇{n}組)'],\r\n    selectAllText: '選取全部',\r\n    deselectAllText: '全部取消',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}